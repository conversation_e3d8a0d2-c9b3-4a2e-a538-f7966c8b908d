<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>主题切换器测试</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <style>
        body {
            font-family: -apple-system, system-ui, sans-serif;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        .test-info h2 {
            color: #495057;
            margin-top: 0;
        }
        .test-info p {
            color: #6c757d;
            line-height: 1.6;
        }
        .debug-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 14px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>主题切换器测试页面</h1>
        
        <div class="test-info">
            <h2>测试说明</h2>
            <p>1. 查看右侧是否有主题切换按钮（🎨 主题）</p>
            <p>2. 点击主题按钮，应该会弹出主题选择面板</p>
            <p>3. 选择不同的主题，背景应该会发生变化</p>
            <p>4. 在移动端测试时，主题面板应该不会被状态栏遮挡</p>
        </div>

        <div class="debug-info" id="debug-info">
            <strong>调试信息：</strong><br>
            页面加载完成，等待主题切换器初始化...
        </div>
    </div>

    <!-- 主题切换器 -->
    <div id="theme-switcher" class="theme-switcher">
        <button id="theme-toggle" class="theme-toggle" title="切换主题">
            <span class="theme-icon">🎨</span>
            <span class="theme-text">主题</span>
        </button>
        <div id="theme-panel" class="theme-panel">
            <div class="theme-panel-header">
                <h3>选择主题</h3>
                <button id="theme-close" class="theme-close">&times;</button>
            </div>
            <div class="theme-grid">
                <div class="theme-option" data-theme="default">
                    <div class="theme-preview theme-preview-default"></div>
                    <span class="theme-name">梦幻紫蓝</span>
                </div>
                <div class="theme-option" data-theme="tech-blue">
                    <div class="theme-preview theme-preview-tech-blue"></div>
                    <span class="theme-name">科技蓝</span>
                </div>
                <div class="theme-option" data-theme="warm-orange">
                    <div class="theme-preview theme-preview-warm-orange"></div>
                    <span class="theme-name">温暖橙</span>
                </div>
                <div class="theme-option" data-theme="forest-green">
                    <div class="theme-preview theme-preview-forest-green"></div>
                    <span class="theme-name">森林绿</span>
                </div>
                <div class="theme-option" data-theme="sakura-pink">
                    <div class="theme-preview theme-preview-sakura-pink"></div>
                    <span class="theme-name">樱花粉</span>
                </div>
                <div class="theme-option" data-theme="deep-space">
                    <div class="theme-preview theme-preview-deep-space"></div>
                    <span class="theme-name">深空紫</span>
                </div>
                <div class="theme-option" data-theme="sunset-yellow">
                    <div class="theme-preview theme-preview-sunset-yellow"></div>
                    <span class="theme-name">日落黄</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入样式和脚本 -->
    <link rel="stylesheet" href="http://localhost:4000/css/background-themes.css" />
    <script src="http://localhost:4000/js/theme-switcher.js"></script>

    <!-- 添加额外的样式来模拟博客环境 -->
    <style>
        /* 模拟博客的container样式 */
        .container {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            transition: background 0.5s ease;
        }

        /* 确保主题切换器可见 */
        .theme-switcher {
            position: fixed !important;
            right: 20px !important;
            top: 50% !important;
            transform: translateY(-50%) !important;
            z-index: 9999 !important;
        }

        .theme-panel {
            z-index: 10000 !important;
        }
    </style>

    <script>
        // 添加调试信息
        function addDebugInfo(message) {
            const debugDiv = document.getElementById('debug-info');
            debugDiv.innerHTML += '<br>' + new Date().toLocaleTimeString() + ': ' + message;
        }

        // 监听主题切换器初始化
        document.addEventListener('DOMContentLoaded', function() {
            addDebugInfo('DOM加载完成');
            
            setTimeout(() => {
                const switcher = document.getElementById('theme-switcher');
                const toggle = document.getElementById('theme-toggle');
                const panel = document.getElementById('theme-panel');
                
                addDebugInfo(`主题切换器元素检查: switcher=${!!switcher}, toggle=${!!toggle}, panel=${!!panel}`);
                
                if (toggle) {
                    toggle.addEventListener('click', function() {
                        addDebugInfo('主题切换按钮被点击');
                    });
                }
            }, 1000);
        });

        // 监听主题变化
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                    addDebugInfo('检测到body类名变化: ' + document.body.className);
                }
            });
        });
        observer.observe(document.body, { attributes: true });
    </script>
</body>
</html>
